package migrator

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/service/user"
)

// FinancialProfileHistoryMigration implements the Migration interface
type FinancialProfileHistoryMigration struct {
	userService user.Service
}

// NewFinancialProfileHistoryMigration creates a new financial profile history migration
func NewFinancialProfileHistoryMigration(userService user.Service) Migration {
	return &FinancialProfileHistoryMigration{
		userService: userService,
	}
}

// Name returns the name of this migration
func (m *FinancialProfileHistoryMigration) Name() string {
	return "financial_profile_history_migration"
}

// Up executes the migration
func (m *FinancialProfileHistoryMigration) Up(ctx context.Context) error {
	log.Println("Starting Financial Profile History Migration...")
	
	err := m.userService.MigrateFinancialProfileHistory(ctx)
	if err != nil {
		log.Printf("Financial Profile History Migration failed: %v", err)
		return err
	}
	
	log.Println("Financial Profile History Migration completed successfully")
	return nil
}
