package user

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/model"
)

// MigrateFinancialProfileHistory creates history records for existing users
// This should be called once to populate the history collection with existing data
func (s *service) MigrateFinancialProfileHistory(ctx context.Context) error {
	// Get all users with financial profiles
	users, err := s.Repository.FindAll(ctx)
	if err != nil {
		return err
	}

	migratedCount := 0
	skippedCount := 0

	for _, user := range users {
		if user.FinancialProfile == nil {
			skippedCount++
			continue
		}

		// Check if history already exists for this user
		existingHistory, err := s.Repository.FindFinancialProfileHistory(ctx, user.ID)
		if err != nil {
			log.Printf("Error checking existing history for user %s: %v", user.ID, err)
			continue
		}

		if len(existingHistory) > 0 {
			skippedCount++
			continue // History already exists, skip this user
		}

		// Create history records based on CreatedAt and UpdatedAt
		// First record: when the profile was created
		if !user.FinancialProfile.CreatedAt.IsZero() {
			createdHistory := &model.FinancialProfileHistory{
				UserID:    user.ID,
				Status:    user.FinancialProfile.Status,
				CreatedAt: user.FinancialProfile.CreatedAt,
			}

			if err := s.Repository.CreateFinancialProfileHistory(ctx, createdHistory); err != nil {
				log.Printf("Error creating created history for user %s: %v", user.ID, err)
				continue
			}
		}

		// Second record: if the profile was updated (and it's different from created date)
		if !user.FinancialProfile.UpdatedAt.IsZero() &&
			!user.FinancialProfile.CreatedAt.Equal(user.FinancialProfile.UpdatedAt) {
			updatedHistory := &model.FinancialProfileHistory{
				UserID:    user.ID,
				Status:    user.FinancialProfile.Status,
				CreatedAt: user.FinancialProfile.UpdatedAt,
			}

			if err := s.Repository.CreateFinancialProfileHistory(ctx, updatedHistory); err != nil {
				log.Printf("Error creating updated history for user %s: %v", user.ID, err)
				continue
			}
		}

		migratedCount++
	}

	log.Printf("Financial profile history migration completed. Migrated: %d, Skipped: %d", migratedCount, skippedCount)
	return nil
}

// MigrateFinancialProfileHistoryForUser creates history records for a specific user
// This is useful for individual user migrations or testing
func (s *service) MigrateFinancialProfileHistoryForUser(ctx context.Context, userID string) error {
	user, err := s.Find(ctx, userID)
	if err != nil {
		return err
	}

	if user.FinancialProfile == nil {
		log.Printf("User %s has no financial profile, skipping migration", userID)
		return nil
	}

	// Check if history already exists for this user
	existingHistory, err := s.Repository.FindFinancialProfileHistory(ctx, userID)
	if err != nil {
		return err
	}

	if len(existingHistory) > 0 {
		log.Printf("User %s already has history records, skipping migration", userID)
		return nil
	}

	// Create history records based on CreatedAt and UpdatedAt
	// First record: when the profile was created
	if !user.FinancialProfile.CreatedAt.IsZero() {
		createdHistory := &model.FinancialProfileHistory{
			UserID:    userID,
			Status:    user.FinancialProfile.Status,
			CreatedAt: user.FinancialProfile.CreatedAt,
		}

		if err := s.Repository.CreateFinancialProfileHistory(ctx, createdHistory); err != nil {
			return err
		}
	}

	// Second record: if the profile was updated (and it's different from created date)
	if !user.FinancialProfile.UpdatedAt.IsZero() &&
		!user.FinancialProfile.CreatedAt.Equal(user.FinancialProfile.UpdatedAt) {
		updatedHistory := &model.FinancialProfileHistory{
			UserID:    userID,
			Status:    user.FinancialProfile.Status,
			CreatedAt: user.FinancialProfile.UpdatedAt,
		}

		if err := s.Repository.CreateFinancialProfileHistory(ctx, updatedHistory); err != nil {
			return err
		}
	}

	log.Printf("Successfully migrated financial profile history for user %s", userID)
	return nil
}
